import App from './App'
import network from './api/network.js'
import * as <PERSON><PERSON> from 'pinia'
// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
Vue.config.productionTip = false
App.mpType = 'app'
const app = new Vue({
  ...App,
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
import WotDesign from 'wot-design-uni'
export function createApp() {
  const app = createSSRApp(App)
  app.use(Pinia.createPinia())
  app.use(WotDesign)
  app.config.globalProperties.$R = network
  return {
    app,
  }
}
// #endif
