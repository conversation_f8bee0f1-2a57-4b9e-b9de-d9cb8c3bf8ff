/* 主色调 */
$primary: #2f56e8; /*蓝色*/
$primary-muted: mix(#000, $primary, 30%);
$primary-light: lighten($primary, 10%);
$themeBrand: #202143; /*主题色*/

$success: #83b24c; /*绿色*/
$success-muted: darken($success, 20%); /*绿色*/
$success-light: lighten($success, 20%); /*绿色*/
$danger: #e2483c; /*红色*/
$danger-muted: darken($danger, 20%); /*红色*/
$danger-light: lighten($danger, 20%); /*红色*/
$warning: #ecb332; /*黄色*/
$warning-muted: darken($warning, 20%); /*黄色*/
$warning-light: lighten($warning, 20%); /*黄色*/
$info: #f1f3f5; /*详情*/
$light: #ebedee; /*跟白色无差的灰色,用于背景*/
$light-detail: #f3f4f8; /*跟白色无差的灰色,用于背景*/
$light-muted: mix(#000, $light, 10%);

/*字体色调*/
$first: #092119; /*字体高强调*/
$second: #646a73; /*灰色*/
$third: #969ba4; /*灰色 用于副标题 备注这种*/
$fourth: #b2b2b2; /*灰色 用于副标题 备注这种*/

/*常用颜色*/
$red: red; /*红色*/
$black: #000; /*黑色*/
$white: #fff; /*白色*/
/*表单色调*/
$placeholder: #b6bac1; /*表单placeholder 的颜色*/
$colorInput: #808080;
$bgInput: #f3f4f8;

/*对应鼠标按下去的颜色*/
$hoverPrimary: #ebebeb;
/*边框色调*/
$borderColor: #ebedee; /*边框浅灰色*/

$padbase: 24rpx;
$marbase: 24rpx;
