export { default as $R } from '@/api/network.js'
const ip = 'demo.easeidea.com/prod-api'
// const ip='************:8080'

function version() {
  const version = __wxConfig.envVersion
  console.log('version', version)
  switch (version) {
    case 'develop':
      return 'https://' + ip
    case 'trial':
      return 'https://' + ip
    case 'release':
      return 'https://' + ip
    default:
      throw Error('服务器地址设置失败！！')
  }
}
export const sys = {
  host: version(),
  ip,
  timeout: 2 * 60 * 1000,
}

export const apiMap = {
  //登录
  login: {
    wxLogin: '/auth/wxLogin', // 登录获取令牌
  },
  //会话
  conversation: {
    getOrCreateByAgent: '/biz/xcx/conversation/getOrCreateByAgent', // 创建会话
    record: '/biz/xcx/conversation/record', // 历史记录
    completions: '/biz/xcx/conversation/completions', // 发起会话
    websocket: 'resource/websocket',
    close: '/biz/xcx/conversation/close', // 中止会话
  },
}
