<template>
  <view class="wot-demo-page">
    <!-- 顶部导航 -->
    <wd-navbar title="Wot-UI 组件展示" left-arrow @click-left="goBack" />
    
    <!-- 内容区域 -->
    <view class="demo-content">
      <!-- 基础组件 -->
      <wd-cell-group title="基础组件">
        <wd-cell title="按钮组件" is-link @click="showButtons = !showButtons" />
        <view v-if="showButtons" class="demo-section">
          <wd-button type="primary" class="demo-button">主要按钮</wd-button>
          <wd-button type="success" class="demo-button">成功按钮</wd-button>
          <wd-button type="warning" class="demo-button">警告按钮</wd-button>
          <wd-button type="error" class="demo-button">错误按钮</wd-button>
        </view>
        
        <wd-cell title="标签组件" is-link @click="showTags = !showTags" />
        <view v-if="showTags" class="demo-section">
          <wd-tag type="primary" class="demo-tag">主要</wd-tag>
          <wd-tag type="success" class="demo-tag">成功</wd-tag>
          <wd-tag type="warning" class="demo-tag">警告</wd-tag>
          <wd-tag type="error" class="demo-tag">错误</wd-tag>
        </view>
      </wd-cell-group>
      
      <!-- 表单组件 -->
      <wd-cell-group title="表单组件">
        <wd-cell title="输入框" is-link @click="showInputs = !showInputs" />
        <view v-if="showInputs" class="demo-section">
          <wd-input v-model="inputValue" placeholder="请输入内容" class="demo-input" />
          <wd-textarea v-model="textareaValue" placeholder="请输入多行内容" class="demo-input" />
        </view>
        
        <wd-cell title="选择器" is-link @click="showPickers = !showPickers" />
        <view v-if="showPickers" class="demo-section">
          <wd-button @click="showPicker" class="demo-button">打开选择器</wd-button>
          <wd-picker
            v-model="pickerValue"
            :show="pickerShow"
            :columns="pickerColumns"
            @confirm="onPickerConfirm"
            @cancel="onPickerCancel"
          />
        </view>
      </wd-cell-group>
      
      <!-- 反馈组件 -->
      <wd-cell-group title="反馈组件">
        <wd-cell title="消息提示" is-link @click="showToast" />
        <wd-cell title="对话框" is-link @click="showDialog" />
        <wd-cell title="加载中" is-link @click="showLoading" />
      </wd-cell-group>
      
      <!-- 展示组件 -->
      <wd-cell-group title="展示组件">
        <wd-cell title="步骤条" is-link @click="showSteps = !showSteps" />
        <view v-if="showSteps" class="demo-section">
          <wd-steps :current="currentStep" class="demo-steps">
            <wd-step title="步骤一" description="这是步骤一的描述" />
            <wd-step title="步骤二" description="这是步骤二的描述" />
            <wd-step title="步骤三" description="这是步骤三的描述" />
          </wd-steps>
          <wd-button @click="nextStep" class="demo-button">下一步</wd-button>
        </view>
        
        <wd-cell title="进度条" is-link @click="showProgress = !showProgress" />
        <view v-if="showProgress" class="demo-section">
          <wd-progress :percentage="progressValue" class="demo-progress" />
          <wd-button @click="updateProgress" class="demo-button">更新进度</wd-button>
        </view>
      </wd-cell-group>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'

// 展示状态
const showButtons = ref(false)
const showTags = ref(false)
const showInputs = ref(false)
const showPickers = ref(false)
const showSteps = ref(false)
const showProgress = ref(false)

// 表单数据
const inputValue = ref('')
const textareaValue = ref('')
const pickerValue = ref([])
const pickerShow = ref(false)
const pickerColumns = ref([
  ['选项1', '选项2', '选项3', '选项4']
])

// 步骤条
const currentStep = ref(0)

// 进度条
const progressValue = ref(30)

// 方法
function goBack() {
  uni.navigateBack()
}

function showPicker() {
  pickerShow.value = true
}

function onPickerConfirm(value) {
  pickerValue.value = value
  pickerShow.value = false
  uni.showToast({
    title: `选择了: ${value.join(', ')}`,
    icon: 'none'
  })
}

function onPickerCancel() {
  pickerShow.value = false
}

function showToast() {
  uni.showToast({
    title: '这是一个消息提示',
    icon: 'success'
  })
}

function showDialog() {
  uni.showModal({
    title: '提示',
    content: '这是一个对话框示例',
    success: (res) => {
      if (res.confirm) {
        uni.showToast({
          title: '点击了确定',
          icon: 'none'
        })
      }
    }
  })
}

function showLoading() {
  uni.showLoading({
    title: '加载中...'
  })
  setTimeout(() => {
    uni.hideLoading()
  }, 2000)
}

function nextStep() {
  if (currentStep.value < 2) {
    currentStep.value++
  } else {
    currentStep.value = 0
  }
}

function updateProgress() {
  progressValue.value = Math.min(progressValue.value + 20, 100)
  if (progressValue.value >= 100) {
    progressValue.value = 0
  }
}
</script>

<style lang="scss" scoped>
.wot-demo-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.demo-content {
  padding: 16px;
}

.demo-section {
  padding: 16px;
  background-color: #fff;
  margin: 8px 0;
  border-radius: 8px;
}

.demo-button {
  margin: 8px;
}

.demo-tag {
  margin: 4px;
}

.demo-input {
  margin: 8px 0;
}

.demo-steps {
  margin: 16px 0;
}

.demo-progress {
  margin: 16px 0;
}
</style>
