<template>
  <view class="pl-4 py-4 width-100 box-border mheight-100vh bg-light">
    <!-- wot-ui 搜索框示例 -->
    <wd-search
      v-model="searchValue"
      placeholder="搜索名片"
      @search="onSearch"
      @clear="onClear"
      class="mb-4"
    />

    <!-- wot-ui 标签页示例 -->
    <wd-tabs v-model="activeTab" @change="onTabChange" class="mb-4">
      <wd-tab title="全部" name="all" />
      <wd-tab title="最近" name="recent" />
      <wd-tab title="收藏" name="favorite" />
    </wd-tabs>

    <!-- 名片列表 -->
    <view
      v-for="(item, index) in filteredList"
      :key="index"
      class="pr-4 box-border span-10 d-inline-block mb-4"
      @tap="onOpencard(item)"
    >
      <view class="width-100 img-box rounded-12 overflow-hidden shadow-md">
        <view class="position-absolute top-0 right-0 bottom-0 left-0">
          <image :src="item.picture" mode="aspectFill" class="width-100 height-100"></image>
        </view>
        <!-- wot-ui 标签示例 -->
        <wd-tag v-if="item.isNew" type="primary" size="small" class="position-absolute top-2 left-2">
          新
        </wd-tag>
      </view>
    </view>

    <!-- wot-ui 演示按钮 -->
    <wd-button type="primary" @click="goToWotDemo" class="demo-btn">
      查看 Wot-UI 组件展示
    </wd-button>

    <!-- wot-ui 浮动按钮示例 -->
    <wd-fab icon="add" @click="onAddCard" />
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
// import {onShow} from "@dcloudio/uni-app"
import mockJson from '@/api/mock-card.json'

// 将对象转换为数组，并添加一些示例属性
const list = ref(Object.values(mockJson).map((item, index) => ({
  ...item,
  isNew: index === 0, // 第一个标记为新
  isRecent: index < 2, // 前两个标记为最近
  isFavorite: index === 1 // 第二个标记为收藏
})))
const searchValue = ref('')
const activeTab = ref('all')

// 过滤后的列表
const filteredList = computed(() => {
  let result = list.value

  // 根据搜索值过滤
  if (searchValue.value) {
    result = result.filter(item =>
      item.name?.toLowerCase().includes(searchValue.value.toLowerCase()) ||
      item.company?.toLowerCase().includes(searchValue.value.toLowerCase())
    )
  }

  // 根据标签页过滤
  if (activeTab.value === 'recent') {
    result = result.filter(item => item.isRecent)
  } else if (activeTab.value === 'favorite') {
    result = result.filter(item => item.isFavorite)
  }

  return result
})

// onShow(()=>{
// 	uni.showLoading({
// 		mask:true,
// 	})
// 	setTimeout(()=>{
// 		uni.hideLoading()
// 	},1000)
// })

function onOpencard(item) {
  uni.navigateTo({
    url: `/pages/cardDetail/cardDetail?id=${item.id}`,
  })
}

function onSearch(value) {
  console.log('Search:', value)
  // todo: 实现搜索逻辑
}

function onClear() {
  searchValue.value = ''
  console.log('Search cleared')
}

function onTabChange(name) {
  console.log('Tab changed to:', name)
  // todo: 实现标签页切换逻辑
}

function onAddCard() {
  uni.showToast({
    title: '添加名片功能',
    icon: 'none'
  })
  // todo: 实现添加名片功能
}

function goToWotDemo() {
  uni.navigateTo({
    url: '/pages/wotDemo/wotDemo'
  })
}
</script>

<style lang="scss" scoped>
.img-box {
  height: 0;
  padding-bottom: 133.33%;
  position: relative;
}

.demo-btn {
  margin: 16px 16px 16px 0;
  width: calc(100% - 16px);
}
</style>
